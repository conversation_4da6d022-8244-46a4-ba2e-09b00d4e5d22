# 证书信息说明文档

## 📋 证书文件概览

两个证书文件，用于打包

| 证书文件 | 平台用途 | 格式 | 创建时间 |
|---------|----------|------|----------|
| `xinningwei_develop_platform.keystore` | Android开发 | PKCS12 | 2025-08-27 |
| `DistributionCer.p12` | iOS发布 | PKCS12 | 2025-08-28 |

**统一密码**: `08270827`

---

## 1️⃣ xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名**: `xinningwei_develop_platform.keystore`
- **别名**: `xinningwei_develop_platform`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: Android应用开发
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
发布者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
```

### 公钥
```
108685436209376221950033777262636233659806245611804085698624579145937713837608306411869942677086931030161886123054425016915389485731922579
655410043615616960992587885728865671117987407060319480698263508324346117911789108258025669956277146710850750852647562455619710437776986
625919039328578942855407602676630270037675986092655065067927348053597426662333782248076238012222728107594164193524942099829771635833081
207304028635865357208474583190331084859567755980181809943972160646576541
```

### MD5
```
0D97C761C7D36EAF43A371F23AD56241
```

---

## 2️⃣ DistributionCer.p12 (iOS发布证书)

### 基本信息
- **文件名**: `DistributionCer.p12`
- **别名**: `developer`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: iOS应用发布
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: C=CN, O="Jiangsu ShangHuTong Information Technology Co., Ltd.", OU=7VTCWFDH4R, CN="iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)", UID=7VTCWFDH4R
发布者: C=US, O=Apple Inc., OU=G3, CN=Apple Worldwide Developer Relations Certification Authority
```



### 公钥密钥
```
C3 D1 BC DD 85 4A F3 F0 21 06 E0 AC 1C 70 42 71 8F 50 FC 7C EE C3 63 1E 5B 03 46 DF 42 06 03 4C 7F A9 E0 5D 4C D2 3B E1 1C C7 F3 F0 8E 58 33 BF 60 AB 18 2E DD 59 CC D0 5C FA 71 AC DC 31 F1 42
3F 58 F5 0C D5 18 90 16 A1 52 81 B6 55 FE 84 02 FF CC D2 7D DD 1A 9D 15 67 78 97 16 0D 16 83 F3 4C 2E F4 6D 33 EE 28 A9 C5 76 96 B0 1D A9 26 2C 24 86 DC C7 FE 6C E4 12 35 C1 7C F8 9C 33 A7 A2
16 41 08 D8 D9 AB A2 14 8D 9A 01 DF 97 58 80 EB 51 0C 09 D3 BD 8D AD F9 9C 14 D3 D8 6B 33 DF AF DF 97 BE 68 BC 1E 8A F6 1F 1E 69 05 5D A3 E1 E8 04 EE 43 B1 F9 B4 FD FD 90 28 38 04 B1 98 24 0B
F9 2F 8D 32 14 9B 7B 1B 6C 8D C6 9B AE 36 3D 67 E7 02 9A 0E 75 2F 4B B3 04 7E 8D BB 6E 25 35 4C 66 44 18 F0 B0 98 67 50 58 34 99 13 59 A1 0A EF 90 59 F0 CA 57 68 81 C8 1A 25 28 49 15 A0 FD BB
```

### sha1
```
1C 18 C2 CB 88 53 53 32 53 D7 85 90 55 EE FF E1 6B 02 52 F9
```

---

## 🔧 使用说明

### 验证证书命令
```bash
# 验证Android证书
keytool -list -v -keystore xinningwei_develop_platform.keystore -storepass 08270827

# 验证iOS发布证书
keytool -list -v -keystore DistributionCer.p12 -storepass 08270827
```

### 公钥格式说明
- **带空格格式**: 便于阅读和文档展示
- **无空格格式**: 便于程序处理和API调用
- **两种格式内容完全相同**，可根据使用场景选择

### 跨设备使用
- ✅ 所有证书文件都支持跨设备使用
- ✅ 只需要正确的密码即可
- ✅ 公钥、MD5、SHA1等信息保持不变
- ✅ 适用于团队协作和CI/CD环境

---

## 📝 注意事项

1. **密码安全**: 妥善保管证书密码 `08270827`
2. **文件备份**: 定期备份证书文件到安全位置
3. **权限控制**: 限制证书文件的访问权限
4. **有效期**: 证书有效期为100年，无需担心过期
5. **团队共享**: 确保团队成员都有相同的证书文件

---

**文档创建时间**: 2025-08-27
**最后更新**: 2025-08-28
