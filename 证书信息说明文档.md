# 证书信息说明文档

## 📋 证书文件概览

两个证书文件，用于打包

| 证书文件 | 平台用途 | 格式 | 创建时间 |
|---------|----------|------|----------|
| `xinningwei_develop_platform.keystore` | Android开发 | PKCS12 | 2025-08-27 |
| `DistributionCer.p12` | iOS发布 | PKCS12 | 2025-08-28 |

**统一密码**: `08270827`

---

## 1️⃣ xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名**: `xinningwei_develop_platform.keystore`
- **别名**: `xinningwei_develop_platform`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: Android应用开发
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
发布者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
```

### 公钥
```
185854734678448247161300250785172797792190312976066268523904569940655928213776187372434381696969096010547092715491999988426707744637
3955436209376221950033777262636233659806245611804085698624579145937713837608306411869942677086931030161886123054425016915389485731922579
6554100436156169609925878857288656711179874070603194806982635083243461179117891082580256699562771467108507508526475624556197104377769
6259190393285789428554076026766302700376759860926550650679273480535974266623337822480762380122227281075941641935249420998297716358330812
0730402863586535720847458319033108485956775598018180994397216064657654
```

### MD5
```
9B521F4D7F56BD3EAA310DD240CA958D
```

---

## 2️⃣ DistributionCer.p12 (iOS发布证书)

### 基本信息
- **文件名**: `DistributionCer.p12`
- **别名**: `developer`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: iOS应用发布
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: C=CN, O="Jiangsu ShangHuTong Information Technology Co., Ltd.", OU=7VTCWFDH4R, CN="iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)", UID=7VTCWFDH4R
发布者: C=US, O=Apple Inc., OU=G3, CN=Apple Worldwide Developer Relations Certification Authority
```



### 公钥密钥
```
C3D1BCDD854AF3F02106E0AC1C7042718F50FC7CEEC3631E5B0346DF4206034C7FA9E05D4CD23BE11CC7F3F08E5833BF60AB182EDD59CCD05CFA71ACDC31F142
3F58F50CD5189016A15281B655FE8402FFCCD27DDD1A9D15677897160D1683F34C2EF46D33EE28A9C57696B01DA9262C2486DCC7FE6CE41235C17CF89C33A7A2
164108D8D9ABA2148D9A01DF975880EB510C09D3BD8DADF99C14D3D86B33DFAFDF97BE68BC1E8AF61F1E69055DA3E1E804EE43B1F9B4FDFD90283804B198240B
F92F8D32149B7B1B6C8DC69BAE363D67E7029A0E752F4BB3047E8DBB6E25354C664418F0B09867505834991359A10AEF9059F0CA576881C81A25284915A0FDBB
```

### 指纹信息
- **MD5**: `D3:19:89:0E:A8:DC:DC:86:6F:C8:73:6D:56:2D:60:86`
- **SHA1**: `1C:18:C2:CB:88:53:53:32:53:D7:85:90:55:EE:FF:E1:6B:02:52:F9`

---

## 🔧 使用说明

### 验证证书命令
```bash
# 验证Android证书
keytool -list -v -keystore xinningwei_develop_platform.keystore -storepass 08270827

# 验证iOS发布证书
keytool -list -v -keystore DistributionCer.p12 -storepass 08270827
```

### 公钥格式说明
- **带空格格式**: 便于阅读和文档展示
- **无空格格式**: 便于程序处理和API调用
- **两种格式内容完全相同**，可根据使用场景选择

### 跨设备使用
- ✅ 所有证书文件都支持跨设备使用
- ✅ 只需要正确的密码即可
- ✅ 公钥、MD5、SHA1等信息保持不变
- ✅ 适用于团队协作和CI/CD环境

---

## 📝 注意事项

1. **密码安全**: 妥善保管证书密码 `08270827`
2. **文件备份**: 定期备份证书文件到安全位置
3. **权限控制**: 限制证书文件的访问权限
4. **有效期**: 证书有效期为100年，无需担心过期
5. **团队共享**: 确保团队成员都有相同的证书文件

---

**文档创建时间**: 2025-08-27
**最后更新**: 2025-08-28
