# 证书信息说明文档

## 📋 证书文件概览

两个个证书文件，用于打包

| 证书文件 | 平台用途 | 格式 | 创建时间 |
|---------|----------|------|----------|
| `xinningwei_develop_platform.keystore` | Android开发 | PKCS12 | 2025-08-27 |
| `xinningwei_ios_develop.p12` | iOS开发 | PKCS12 | 2025-08-27 |

**统一密码**: `08270827`

---

## 1️⃣ xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名**: `xinningwei_develop_platform.keystore`
- **别名**: `xinningwei_develop_platform`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: Android应用开发
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
发布者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
```

### 公钥密钥
```
30 82 01 22 30 0d 06 09 2a 86 48 86 f7 0d 01 01 01 05 00 03 82 01 0f 00 30 82 01 0a 02 82 01 01
00 97 3b e5 11 28 e9 70 1d cf a6 9a 70 0d 8f 0d fd 83 c1 fc 4c 19 42 97 70 f9 8e d6 ab f2 6c cd
ec 7e d7 4c 19 b7 c5 34 0b 75 2f b2 0b c8 25 ec 2d 48 fd 6c 07 15 57 d8 4b c3 91 b1 12 fe b3 9f
dc 3b 12 9d f9 61 40 59 d0 e8 09 a2 a6 1f ef e8 39 a1 b8 65 a0 9c c1 d3 60 92 de b1 0c 0b 35 d5
3d 54 e1 e2 69 34 c6 fd 2d 02 61 9b 06 f4 a2 49 8c d2 5e 43 48 75 1f 36 87 7c 6b 78 27 bf 04 b0
74 03 64 fa a6 f9 0d a4 b5 fd fd 09 5f 97 ab db e0 00 7a 8d 54 90 d7 09 9b 3a a9 6a 69 bf 0e 6b
4c a2 ae c4 3b 40 96 d2 18 d6 70 d1 f2 ac ba 47 be 6d b5 1d d4 4c 36 74 5c 40 23 c1 ed eb 99 d9
e4 3b 8e a2 e2 1c c4 50 0b 26 31 43 23 46 5a e7 69 42 4e e3 a3 df 38 cb d9 85 ff dd 88 63 48 8b
f8 6c 22 f5 9c ef ac 7a 4e dc 43 36 bf 3c 91 4a dc 9e 5f f4 2d e9 3c 40 73 54 56 17 d8 b4 e4 eb
1d 02 03 01 00 01
```

### 指纹信息
- **MD5**: `0D 97 C7 61 C7 D3 6E AF 43 A3 71 F2 3A D5 62 41`
- **SHA1**: `B6 63 DC D1 8E E3 37 4A D9 2F 2D 13 5C 13 03 65 E3 02 A8 18`

---

## 2️⃣ xinningwei_ios_develop.p12 (iOS开发证书)

### 基本信息
- **文件名**: `xinningwei_ios_develop.p12`
- **别名**: `xinningwei_ios_develop`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: iOS应用开发
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
发布者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
```

### 公钥密钥
```
30 82 01 22 30 0d 06 09 2a 86 48 86 f7 0d 01 01 01 05 00 03 82 01 0f 00 30 82 01 0a 02 82 01 01
00 b4 c0 7a 81 a6 92 7f 00 da 9b 60 95 d8 e4 a9 92 23 1e 22 6b 27 0f 15 39 a8 2c 21 d8 58 e3 a3
43 61 a2 40 88 8e eb 6a 91 66 bb a1 ee 3e e2 9f a6 e2 02 6f 1f c6 d1 da 03 79 c9 c4 59 6d e6 0b
83 e1 58 af 14 36 c3 bc 71 1f 9d 64 23 68 bf 14 cf 89 53 9c 87 a8 37 48 25 41 a4 fa bb 47 59 7b
16 a9 28 17 c5 c6 44 50 48 8c d2 4c fd ad 43 50 9b e4 ef 68 2c a7 60 4f 77 ac 29 43 9e 03 a4 9d
d4 12 7a 4a bf 38 d1 14 ed d6 39 4e 89 75 2d b6 e3 f5 62 0b 24 38 a6 06 57 56 62 4b d8 09 d3 45
b9 34 e8 2d 94 a5 9e 50 ea 24 c9 ee fe 17 44 c5 83 22 e6 7a 62 8f e8 a6 a2 f2 5c 52 2e 00 c6 96
d5 fb 4b a8 0a 50 ef 1a 2a f2 e3 65 f6 d1 74 40 38 11 6d d1 bf dd 0e 1a 27 06 9b 9c b3 74 8a 3c
d7 b3 ac 25 3b a1 62 21 c1 37 59 4c fc b7 33 d1 9e 4b 58 63 1f d0 50 a1 c3 00 ec 20 c1 1b cd a9
73 02 03 01 00 01
```

### 指纹信息
- **MD5**: `A6 36 EE 1B 4B 33 C8 DA 68 9F 24 E5 65 0F 6D E0`
- **SHA1**: `EE 41 C9 EC 55 9F C1 C2 65 5F 77 FF 01 88 36 40 C0 52 19 7E`

---

## 🔧 使用说明

### 验证证书命令
```bash
# 验证Android证书
keytool -list -v -keystore xinningwei_develop_platform.keystore -storepass 08270827

# 验证iOS证书
keytool -list -v -keystore xinningwei_ios_develop.p12 -storepass 08270827
```

### 公钥格式说明
- **带空格格式**: 便于阅读和文档展示
- **无空格格式**: 便于程序处理和API调用
- **两种格式内容完全相同**，可根据使用场景选择

### 跨设备使用
- ✅ 所有证书文件都支持跨设备使用
- ✅ 只需要正确的密码即可
- ✅ 公钥、MD5、SHA1等信息保持不变
- ✅ 适用于团队协作和CI/CD环境

---

## 📝 注意事项

1. **密码安全**: 妥善保管证书密码 `08270827`
2. **文件备份**: 定期备份证书文件到安全位置
3. **权限控制**: 限制证书文件的访问权限
4. **有效期**: 证书有效期为100年，无需担心过期
5. **团队共享**: 确保团队成员都有相同的证书文件

---

**文档创建时间**: 2025-08-27
**最后更新**: 2025-08-27
