# 证书信息说明文档

## 📋 证书文件概览

两个证书文件，用于打包

| 证书文件 | 平台用途 | 格式 | 创建时间 |
|---------|----------|------|----------|
| `xinningwei_develop_platform.keystore` | Android开发 | PKCS12 | 2025-08-27 |
| `DistributionCer.p12` | iOS发布 | PKCS12 | 2025-08-28 |

**统一密码**: `08270827`

---

## 1️⃣ xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名**: `xinningwei_develop_platform.keystore`
- **别名**: `xinningwei_develop_platform`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: Android应用开发
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
发布者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
```

### 公钥密钥
```
30 82 01 22 30 0d 06 09 2a 86 48 86 f7 0d 01 01 01 05 00 03 82 01 0f 00 30 82 01 0a 02 82 01 01
00 97 3b e5 11 28 e9 70 1d cf a6 9a 70 0d 8f 0d fd 83 c1 fc 4c 19 42 97 70 f9 8e d6 ab f2 6c cd
ec 7e d7 4c 19 b7 c5 34 0b 75 2f b2 0b c8 25 ec 2d 48 fd 6c 07 15 57 d8 4b c3 91 b1 12 fe b3 9f
dc 3b 12 9d f9 61 40 59 d0 e8 09 a2 a6 1f ef e8 39 a1 b8 65 a0 9c c1 d3 60 92 de b1 0c 0b 35 d5
3d 54 e1 e2 69 34 c6 fd 2d 02 61 9b 06 f4 a2 49 8c d2 5e 43 48 75 1f 36 87 7c 6b 78 27 bf 04 b0
74 03 64 fa a6 f9 0d a4 b5 fd fd 09 5f 97 ab db e0 00 7a 8d 54 90 d7 09 9b 3a a9 6a 69 bf 0e 6b
4c a2 ae c4 3b 40 96 d2 18 d6 70 d1 f2 ac ba 47 be 6d b5 1d d4 4c 36 74 5c 40 23 c1 ed eb 99 d9
e4 3b 8e a2 e2 1c c4 50 0b 26 31 43 23 46 5a e7 69 42 4e e3 a3 df 38 cb d9 85 ff dd 88 63 48 8b
f8 6c 22 f5 9c ef ac 7a 4e dc 43 36 bf 3c 91 4a dc 9e 5f f4 2d e9 3c 40 73 54 56 17 d8 b4 e4 eb
1d 02 03 01 00 01
```

### 指纹信息
- **MD5**: `0D 97 C7 61 C7 D3 6E AF 43 A3 71 F2 3A D5 62 41`
- **SHA1**: `B6 63 DC D1 8E E3 37 4A D9 2F 2D 13 5C 13 03 65 E3 02 A8 18`

---

## 2️⃣ DistributionCer.p12 (iOS发布证书)

### 基本信息
- **文件名**: `DistributionCer.p12`
- **别名**: `developer`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: iOS应用发布
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: C=CN, O="Jiangsu ShangHuTong Information Technology Co., Ltd.", OU=7VTCWFDH4R, CN="iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)", UID=7VTCWFDH4R
发布者: C=US, O=Apple Inc., OU=G3, CN=Apple Worldwide Developer Relations Certification Authority
```



### 公钥密钥
```
C3D1BCDD854AF3F02106E0AC1C704271 8F50FC7CEEC3631E5B0346DF4206034C 7FA9E05D4CD23BE11CC7F3F08E5833BF 60AB182EDD59CCD05CFA71ACDC31F142
3F58F50CD5189016A15281B655FE8402 FFCCD27DDD1A9D15677897160D1683F3 4C2EF46D33EE28A9C57696B01DA9262C 2486DCC7FE6CE41235C17CF89C33A7A2
164108D8D9ABA2148D9A01DF975880EB 510C09D3BD8DADF99C14D3D86B33DFAF DF97BE68BC1E8AF61F1E69055DA3E1E8 04EE43B1F9B4FDFD902838048198240B
F92F8D32149B7B1B6C8DC69BAE363D67 E7029A0E752F4BB3047E8DBB6E25354C 664418F0B0986750583499135A10AEF 9059F0CA576881C81A252849115A0FDBB
```

### 指纹信息
- **MD5**: `D3 19 89 0E A8 DC DC 86 6F C8 73 6D 56 2D 60 86`
- **SHA1**: `1C 18 C2 CB 88 53 53 32 53 D7 85 90 55 EE FF E1 6B 02 52 F9`

---

## 🔧 使用说明

### 验证证书命令
```bash
# 验证Android证书
keytool -list -v -keystore xinningwei_develop_platform.keystore -storepass 08270827

# 验证iOS发布证书
keytool -list -v -keystore DistributionCer.p12 -storepass 08270827
```

### 公钥格式说明
- **带空格格式**: 便于阅读和文档展示
- **无空格格式**: 便于程序处理和API调用
- **两种格式内容完全相同**，可根据使用场景选择

### 跨设备使用
- ✅ 所有证书文件都支持跨设备使用
- ✅ 只需要正确的密码即可
- ✅ 公钥、MD5、SHA1等信息保持不变
- ✅ 适用于团队协作和CI/CD环境

---

## 📝 注意事项

1. **密码安全**: 妥善保管证书密码 `08270827`
2. **文件备份**: 定期备份证书文件到安全位置
3. **权限控制**: 限制证书文件的访问权限
4. **有效期**: 证书有效期为100年，无需担心过期
5. **团队共享**: 确保团队成员都有相同的证书文件

---

**文档创建时间**: 2025-08-27
**最后更新**: 2025-08-28
