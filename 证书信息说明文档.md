# 证书信息说明文档

## 📋 证书文件概览

两个证书文件，用于打包

| 证书文件 | 平台用途 | 格式 | 创建时间 |
|---------|----------|------|----------|
| `xinningwei_develop_platform.keystore` | Android开发 | PKCS12 | 2025-08-27 |
| `DistributionCer.p12` | iOS发布 | PKCS12 | 2025-08-28 |

**统一密码**: `08270827`

---

## 1️⃣ xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名**: `xinningwei_develop_platform.keystore`
- **别名**: `xinningwei_develop_platform`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: Android应用开发
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
发布者: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
```

### 公钥
```
973be51128e9701dcfa69a700d8f0dfd83c1fc4c194297709f8ed6abf26ccdec7ed74c19b7c5340b752fb20bc825ec2d48fd6c071557d84bc391b112feb39fdc3b129df9614059d0e809a2a61fefe839a1b865a09cc1d36092deb10c0b35d53d54e1e26934c6fd2d02619b06f4a2498cd25e4348751f36877c6b7827bf04b074036
4faa6f90da4b5fdfd095f97abdbe0007a8d5490d7099b3aa96a69bf0e6b4ca2aec43b4096d218d670d1f2acba47be6db51dd44c36745c4023c1edeb99d9e43b8ea2e21cc4500b2631432346
5ae769424ee3a3df38cbd985ffdd8863488bf86c22f59cefac7a4edc4336bf3c914adc9e5ff42de93c40735456
17d8b4e4eb1d
```

### MD5
```
0D97C761C7D36EAF43A371F23AD56241
```

---

## 2️⃣ DistributionCer.p12 (iOS发布证书)

### 基本信息
- **文件名**: `DistributionCer.p12`
- **别名**: `developer`
- **密码**: `08270827`
- **格式**: PKCS12
- **用途**: iOS应用发布
- **包名**: `com.xinningwei.WanShangYunPro`

### 证书主体信息
```
所有者: C=CN, O="Jiangsu ShangHuTong Information Technology Co., Ltd.", OU=7VTCWFDH4R, CN="iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)", UID=7VTCWFDH4R
发布者: C=US, O=Apple Inc., OU=G3, CN=Apple Worldwide Developer Relations Certification Authority
```



### 公钥密钥
```
CC 97 10 DF 1E 94 DA F2 D8 71 F7 AF 3A 46 00 3F CE 9B F6 17 27 03 EA BE 3C AD 6A 0A 63 7C 99 FF 50 29 12 5C 32 50 8B E7 EB C5 EE 98 48 4E A4 6A 27 A9 A5
DA D2 33 E5 1F 8A 63 DD F9 EE 96 5D B4 F2 02 70 57 C5 38 3E CA 1B 99 6B 9B 79 0D AE 14 EE DF E7 D2 6E 8A 92 7D 47 7A 4B 6C 41 88 55 D8 FA 53 5D 37 A1 D3
35 15 4F 3C 41 87 A7 8E 63 EC B6 E9 AA C3 82 D9 76 32 F5 06 A1 3D 63 A6 3A D5 0F E5 2B FB F4 02 C9 AE EC B9 0F 8B F2 03 4B 49 60 08 DB 06 00 FD 11 CE 6F
7F DC 61 3A 3C B8 EA 1D 0A B8 51 11 3A D5 56 DD 07 39 BF 29 B0 33 1B 89 76 26 97 E7 8D 68 7A 7B A0 1D 62 38 55 28 42 DC 59 BC D4 A3 92 76
8B 5A EF 99 98 B0 F0 5C 19 F3 B9 45 6E 0D 07 B6 76 C8 A5 CD B9 99 32 14 87 0C 83 E1 47 51 67 85 90 67 35 6E EB EC 40 29 E0 58 CD FC E5 96 55 44 07 37 8D
```

### sha1
```
84 C1 0E 87 EF D2 8D 03 4B B5 79 FB D0 4F 3C 65 E2 98 C2 B2
```

---

## 🔧 使用说明

### 验证证书命令
```bash
# 验证Android证书
keytool -list -v -keystore xinningwei_develop_platform.keystore -storepass 08270827

# 验证iOS发布证书
keytool -list -v -keystore DistributionCer.p12 -storepass 08270827
```

### 公钥格式说明
- **带空格格式**: 便于阅读和文档展示
- **无空格格式**: 便于程序处理和API调用
- **两种格式内容完全相同**，可根据使用场景选择

### 跨设备使用
- ✅ 所有证书文件都支持跨设备使用
- ✅ 只需要正确的密码即可
- ✅ 公钥、MD5、SHA1等信息保持不变
- ✅ 适用于团队协作和CI/CD环境

---

## 📝 注意事项

1. **密码安全**: 妥善保管证书密码 `08270827`
2. **文件备份**: 定期备份证书文件到安全位置
3. **权限控制**: 限制证书文件的访问权限
4. **有效期**: 证书有效期为100年，无需担心过期
5. **团队共享**: 确保团队成员都有相同的证书文件

---

**文档创建时间**: 2025-08-27
**最后更新**: 2025-08-28
